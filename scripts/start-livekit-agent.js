#!/usr/bin/env node

/**
 * Livekit Agent Startup Script
 * This script starts the Livekit Voice Transcription Agent
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { spawn } from 'child_process';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '..', '.env') });

// Validate required environment variables
const requiredEnvVars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => {
        console.error(`   - ${envVar}`);
    });
    console.error('\nPlease check your .env file and ensure all Livekit variables are set.');
    process.exit(1);
}

console.log('🚀 Starting Livekit Voice Transcription Agent...');
console.log(`📡 Livekit URL: ${process.env.LIVEKIT_URL}`);
console.log(`🔑 API Key: ${process.env.LIVEKIT_API_KEY?.substring(0, 8)}...`);

// Path to the agent file
const agentPath = join(__dirname, '..', 'app', 'lib', 'livekit-agent.mjs');

// Start the agent process in development mode
const agentProcess = spawn('node', [agentPath, 'dev'], {
    stdio: 'inherit',
    env: {
        ...process.env,
        NODE_ENV: process.env.NODE_ENV || 'development',
    }
});

// Handle process events
agentProcess.on('error', (error) => {
    console.error('❌ Failed to start agent:', error);
    process.exit(1);
});

agentProcess.on('exit', (code, signal) => {
    if (signal) {
        console.log(`🛑 Agent process terminated by signal: ${signal}`);
    } else {
        console.log(`🏁 Agent process exited with code: ${code}`);
    }
    process.exit(code || 0);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, shutting down agent...');
    agentProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down agent...');
    agentProcess.kill('SIGTERM');
});

console.log('✅ Agent startup script is running. Press Ctrl+C to stop.');
