# Livekit Agent Integration

This document describes the Livekit Agent integration that replaces the browser-based Web Speech API dictation with a server-side Livekit Agent for voice transcription.

## Overview

The integration consists of several components:

1. **Livekit Agent Server** (`app/lib/livekit-agent.js`) - Handles voice input and transcription
2. **Token Generation API** (`app/api/livekit/token/route.js`) - Generates access tokens for client connections
3. **Voice Input Component** (`app/components/LivekitVoiceInput.js`) - React hook for managing Livekit connections
4. **Modified ChatForm** (`app/components/ChatForm.js`) - Updated to use Livekit instead of Web Speech API

## How It Works

1. **User clicks microphone button** → Triggers connection to Livekit room
2. **Token generation** → API generates secure access token for the room
3. **Room connection** → Client connects to Livekit room using WebRTC
4. **Agent connection** → Livekit Agent joins the same room
5. **Audio streaming** → User's microphone audio is streamed to the agent
6. **Transcription** → Agent processes audio through STT (Deepgram/OpenAI Whisper)
7. **Result delivery** → Transcribed text is sent back to client via data messages
8. **UI update** → Transcribed text appears in the chat input field

## Environment Variables

Required environment variables (already configured in `.env`):

```bash
LIVEKIT_URL="wss://apologist-agent-up1jdswz.livekit.cloud"
LIVEKIT_API_KEY="APIt6Xh9LCvkEYM"
LIVEKIT_API_SECRET="IilG3OtefheQJJTNbWIeeixGI1HKTt0vTwdPogUZlbKE"
```

## Usage

### Starting the Agent Server

```bash
# Start the Livekit agent
npm run agent

# Or for development
npm run agent:dev
```

### Using the Voice Input

1. Click the microphone button in the chat interface
2. The button will show different states:
   - **Gray**: Disconnected
   - **Yellow**: Connecting
   - **Blue/Primary**: Connected and listening
   - **Red**: Error state
3. Speak into your microphone
4. The transcribed text will appear in the chat input
5. Click the microphone button again to stop and disconnect

## Technical Details

### Connection States

- `disconnected` - Not connected to Livekit
- `connecting` - Establishing connection
- `connected` - Connected but not listening
- `listening` - Actively transcribing audio
- `error` - Error occurred

### Audio Processing

- Uses WebRTC for real-time audio streaming
- Supports multiple STT providers (Deepgram, OpenAI Whisper)
- Provides both interim and final transcription results
- Automatic silence detection and session management

### Security

- JWT-based authentication for room access
- Unique room names for each session
- Secure token generation with proper permissions
- Audio data is processed server-side, not stored

## Troubleshooting

### Common Issues

1. **Microphone not working**
   - Check browser permissions for microphone access
   - Ensure HTTPS is used (required for microphone access)

2. **Connection fails**
   - Verify Livekit environment variables are set
   - Check network connectivity
   - Ensure Livekit server is accessible

3. **No transcription**
   - Verify the Livekit agent is running (`npm run agent`)
   - Check browser console for errors
   - Ensure STT service (Deepgram/OpenAI) credentials are valid

4. **Audio quality issues**
   - Check microphone settings and permissions
   - Ensure stable internet connection
   - Try refreshing the page

### Debug Information

The integration provides detailed logging:
- Browser console shows connection states and errors
- Agent server logs transcription events
- Network tab shows WebRTC connection details

## Testing

To test the integration:

1. Start the Next.js development server: `npm run dev`
2. Start the Livekit agent: `npm run agent`
3. Open the application in a browser
4. Click the microphone button and speak
5. Verify transcribed text appears in the input field

## Migration from Web Speech API

The integration maintains the same user interface and behavior as the previous Web Speech API implementation:

- Same microphone button location and styling
- Same keyboard shortcuts and accessibility features
- Same transcription result format (adds "?" at the end)
- Improved reliability and cross-browser compatibility

## Dependencies

New dependencies added for this integration:

```json
{
  "@livekit/agents": "^1.0.3",
  "@livekit/agents-plugin-deepgram": "^1.0.3",
  "@livekit/agents-plugin-openai": "^1.0.3",
  "@livekit/agents-plugin-silero": "^1.0.3",
  "@livekit/components-react": "^2.9.14",
  "@livekit/components-styles": "^1.1.6",
  "livekit-client": "^2.9.14",
  "livekit-server-sdk": "^2.9.14"
}
```
