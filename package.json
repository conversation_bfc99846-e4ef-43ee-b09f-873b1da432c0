{"name": "apg-agent", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "npm run lint && npm run build", "migrate": "node -r esbuild-register db/migrate.ts", "agent": "node scripts/start-livekit-agent.js", "agent:dev": "NODE_ENV=development node scripts/start-livekit-agent.js"}, "dependencies": {"@ai-sdk/anthropic": "*", "@ai-sdk/deepseek": "*", "@ai-sdk/fireworks": "*", "@ai-sdk/google": "*", "@ai-sdk/groq": "*", "@ai-sdk/openai": "*", "@ai-sdk/react": "^*", "@ai-sdk/togetherai": "*", "@ai-sdk/ui-utils": "^1.2.11", "@ai-sdk/xai": "*", "@ceol/tailwind-tooltip": "^0.1.4", "@designbycode/tailwindcss-text-shadow": "^2.1.0", "@headlessui/react": "^2.1.3", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.0", "@livekit/agents": "^1.0.3", "@livekit/agents-plugin-deepgram": "^1.0.3", "@livekit/agents-plugin-elevenlabs": "^1.0.3", "@livekit/agents-plugin-openai": "^1.0.3", "@livekit/agents-plugin-silero": "^1.0.3", "@livekit/components-react": "^2.9.14", "@livekit/components-styles": "^1.1.6", "@neondatabase/serverless": "^0.10.4", "@next/third-parties": "^14.2.7", "@upstash/redis": "^1.35.3", "@vercel/analytics": "^1.3.1", "@vercel/functions": "^1.5.2", "@vercel/postgres": "^0.9.0", "@vercel/speed-insights": "^1.0.12", "accept-language": "^3.0.20", "ai": "*", "ajv": "^8.17.1", "copy-to-clipboard": "^3.3.3", "crypto": "^1.0.1", "dotenv": "^16.6.1", "drizzle-orm": "^0.41.0", "esbuild-register": "^3.6.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.7", "html-entities": "^2.5.2", "i18next": "^23.14.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-resources-to-backend": "^1.2.1", "jose": "^5.9.6", "jsdom": "^26.1.0", "livekit-client": "^2.15.7", "livekit-server-sdk": "^2.13.3", "next": "^14.2.21", "nextjs-hotjar": "^1.2.0", "node-telegram-bot-api": "^0.63.0", "react": "^18.3.1", "react-cookie": "^7.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.1", "react-textarea-autosize": "^8.5.3", "remove-markdown": "^0.6.0", "showdown": "^2.1.0", "tinycolor2": "^1.6.0", "twilio": "^5.7.1", "util": "^0.12.5"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.4", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.15", "drizzle-kit": "^0.31.4", "encoding": "^0.1.13", "postcss": "^8.4.21", "postgres": "^3.4.7", "tailwindcss": "^3.4.10", "tsx": "^4.19.3"}, "browser": {"crypto": false, "stream": false}, "optionalDependencies": {"@esbuild/linux-x64": "^0.25.9"}}