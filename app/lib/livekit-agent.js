import { Agent, WorkerOptions, cli } from '@livekit/agents';
import { deepgram } from '@livekit/agents-plugin-deepgram';
import { openai } from '@livekit/agents-plugin-openai';
import { silero } from '@livekit/agents-plugin-silero';
import { AudioSource, RoomEvent, TrackKind } from 'livekit-client';

/**
 * Livekit Agent for handling voice input and transcription
 * This agent connects to a Livekit room, listens for audio input,
 * transcribes it using STT, and sends the transcription back to the client
 */
class VoiceTranscriptionAgent extends Agent {
    constructor() {
        super();
        this.stt = null;
        this.transcriptionBuffer = '';
        this.isListening = false;
    }

    async initialize() {
        // Initialize STT with Deepgram (fallback to OpenAI Whisper if needed)
        try {
            this.stt = new deepgram.STT({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
            });
        } catch (error) {
            console.warn('Deepgram STT not available, falling back to OpenAI Whisper:', error);
            this.stt = new openai.STT({
                model: 'whisper-1',
            });
        }
    }

    async connect(room) {
        await super.connect(room);
        
        // Listen for participant connections
        room.on(RoomEvent.ParticipantConnected, (participant) => {
            console.log(`Participant connected: ${participant.identity}`);
            this.handleParticipantConnected(participant);
        });

        // Listen for track subscriptions (audio tracks)
        room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
            if (track.kind === TrackKind.Audio) {
                console.log(`Audio track subscribed from ${participant.identity}`);
                this.handleAudioTrack(track, participant);
            }
        });

        // Send initial status message
        await this.sendDataMessage({
            type: 'agent_status',
            status: 'connected',
            message: 'Voice transcription agent is ready'
        });
    }

    async handleParticipantConnected(participant) {
        // Send welcome message to the participant
        await this.sendDataMessage({
            type: 'welcome',
            message: 'Voice transcription agent connected. Start speaking to begin transcription.'
        });
    }

    async handleAudioTrack(track, participant) {
        if (!this.stt) {
            console.error('STT not initialized');
            return;
        }

        try {
            // Create audio stream from track
            const audioStream = track.createAudioStream();
            
            // Start transcription
            this.isListening = true;
            await this.sendDataMessage({
                type: 'transcription_status',
                status: 'listening',
                message: 'Listening for speech...'
            });

            // Process audio stream through STT
            const transcriptionStream = this.stt.transcribe(audioStream);
            
            transcriptionStream.on('data', async (result) => {
                if (result.is_final) {
                    // Final transcription result
                    const transcription = result.alternatives[0]?.transcript || '';
                    if (transcription.trim()) {
                        console.log(`Final transcription: ${transcription}`);
                        
                        // Send final transcription to client
                        await this.sendDataMessage({
                            type: 'transcription_final',
                            text: transcription,
                            confidence: result.alternatives[0]?.confidence || 0
                        });

                        // Reset buffer
                        this.transcriptionBuffer = '';
                    }
                } else {
                    // Interim results
                    const transcription = result.alternatives[0]?.transcript || '';
                    if (transcription.trim()) {
                        this.transcriptionBuffer = transcription;
                        
                        // Send interim transcription to client
                        await this.sendDataMessage({
                            type: 'transcription_interim',
                            text: transcription,
                            confidence: result.alternatives[0]?.confidence || 0
                        });
                    }
                }
            });

            transcriptionStream.on('error', async (error) => {
                console.error('Transcription error:', error);
                await this.sendDataMessage({
                    type: 'transcription_error',
                    error: error.message
                });
            });

            transcriptionStream.on('end', async () => {
                console.log('Transcription stream ended');
                this.isListening = false;
                await this.sendDataMessage({
                    type: 'transcription_status',
                    status: 'stopped',
                    message: 'Transcription stopped'
                });
            });

        } catch (error) {
            console.error('Error handling audio track:', error);
            await this.sendDataMessage({
                type: 'transcription_error',
                error: error.message
            });
        }
    }

    async sendDataMessage(data) {
        try {
            const message = JSON.stringify(data);
            await this.room.localParticipant.publishData(
                new TextEncoder().encode(message),
                { reliable: true }
            );
        } catch (error) {
            console.error('Error sending data message:', error);
        }
    }

    async disconnect() {
        this.isListening = false;
        await super.disconnect();
    }
}

/**
 * Entry point for the Livekit Agent
 */
async function entrypoint(ctx) {
    const agent = new VoiceTranscriptionAgent();
    await agent.initialize();
    await agent.start(ctx);
}

// Export for use in API routes
export { VoiceTranscriptionAgent, entrypoint };

// CLI entry point for standalone agent
if (import.meta.url === `file://${process.argv[1]}`) {
    cli.runApp(new WorkerOptions({
        entrypoint_fnc: entrypoint,
        prewarm_fnc: async () => {
            console.log('Prewarming Livekit Voice Transcription Agent...');
        }
    }));
}
