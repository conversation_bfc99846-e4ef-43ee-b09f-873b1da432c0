import {
    WorkerOptions,
    cli,
    defineAgent,
} from '@livekit/agents';
import * as deepgram from '@livekit/agents-plugin-deepgram';
import * as openai from '@livekit/agents-plugin-openai';
import * as silero from '@livekit/agents-plugin-silero';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Livekit Agent for handling voice input and transcription
 * This agent connects to a Livekit room, listens for audio input,
 * transcribes it using STT, and sends the transcription back to the client
 */
class VoiceTranscriptionAgent {
    constructor() {
        this.stt = null;
        this.transcriptionBuffer = '';
        this.isListening = false;
    }

    async initialize() {
        // Initialize STT with Deepgram (fallback to OpenAI Whisper if needed)
        try {
            this.stt = new deepgram.STT({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
            });
        } catch (error) {
            console.warn('Deepgram STT not available, falling back to OpenAI Whisper:', error);
            this.stt = new openai.STT({
                model: 'whisper-1',
            });
        }
    }

    async handleRoom(room) {
        console.log('Agent connected to room:', room.name);

        // Listen for participant connections
        room.on('participantConnected', (participant) => {
            console.log(`Participant connected: ${participant.identity}`);
            this.handleParticipantConnected(participant, room);
        });

        // Listen for track subscriptions (audio tracks)
        room.on('trackSubscribed', (track, publication, participant) => {
            if (track.kind === 'audio') {
                console.log(`Audio track subscribed from ${participant.identity}`);
                this.handleAudioTrack(track, participant, room);
            }
        });

        // Send initial status message
        await this.sendDataMessage(room, {
            type: 'agent_status',
            status: 'connected',
            message: 'Voice transcription agent is ready'
        });
    }

    async handleParticipantConnected(participant, room) {
        // Send welcome message to the participant
        await this.sendDataMessage(room, {
            type: 'welcome',
            message: 'Voice transcription agent connected. Start speaking to begin transcription.'
        });
    }

    async handleAudioTrack(track, participant, room) {
        if (!this.stt) {
            console.error('STT not initialized');
            return;
        }

        try {
            // Create audio stream from track
            const audioStream = track.createAudioStream();
            
            // Start transcription
            this.isListening = true;
            await this.sendDataMessage(room, {
                type: 'transcription_status',
                status: 'listening',
                message: 'Listening for speech...'
            });

            // Process audio stream through STT
            const transcriptionStream = this.stt.transcribe(audioStream);

            transcriptionStream.on('data', async (result) => {
                if (result.is_final) {
                    // Final transcription result
                    const transcription = result.alternatives[0]?.transcript || '';
                    if (transcription.trim()) {
                        console.log(`Final transcription: ${transcription}`);

                        // Send final transcription to client
                        await this.sendDataMessage(room, {
                            type: 'transcription_final',
                            text: transcription,
                            confidence: result.alternatives[0]?.confidence || 0
                        });

                        // Reset buffer
                        this.transcriptionBuffer = '';
                    }
                } else {
                    // Interim results
                    const transcription = result.alternatives[0]?.transcript || '';
                    if (transcription.trim()) {
                        this.transcriptionBuffer = transcription;

                        // Send interim transcription to client
                        await this.sendDataMessage(room, {
                            type: 'transcription_interim',
                            text: transcription,
                            confidence: result.alternatives[0]?.confidence || 0
                        });
                    }
                }
            });

            transcriptionStream.on('error', async (error) => {
                console.error('Transcription error:', error);
                await this.sendDataMessage(room, {
                    type: 'transcription_error',
                    error: error.message
                });
            });

            transcriptionStream.on('end', async () => {
                console.log('Transcription stream ended');
                this.isListening = false;
                await this.sendDataMessage(room, {
                    type: 'transcription_status',
                    status: 'stopped',
                    message: 'Transcription stopped'
                });
            });

        } catch (error) {
            console.error('Error handling audio track:', error);
            await this.sendDataMessage(room, {
                type: 'transcription_error',
                error: error.message
            });
        }
    }

    async sendDataMessage(room, data) {
        try {
            const message = JSON.stringify(data);
            await room.localParticipant.publishData(
                new TextEncoder().encode(message),
                { reliable: true }
            );
        } catch (error) {
            console.error('Error sending data message:', error);
        }
    }

    async disconnect() {
        this.isListening = false;
    }
}

/**
 * Entry point for the Livekit Agent
 */
async function entrypoint(ctx) {
    console.log('Voice transcription agent starting...');

    await ctx.connect();
    console.log('Connected to room:', ctx.room.name);

    const agent = new VoiceTranscriptionAgent();
    await agent.initialize();
    await agent.handleRoom(ctx.room);

    // Wait for participant to join
    const participant = await ctx.waitForParticipant();
    console.log('Participant joined:', participant.identity);
}

// Define the agent using the new API
export default defineAgent({
    entry: entrypoint,
});

// Export for use in API routes
export { VoiceTranscriptionAgent, entrypoint };

// CLI entry point for standalone agent
if (import.meta.url === `file://${process.argv[1]}`) {
    cli.runApp(new WorkerOptions({
        agent: fileURLToPath(import.meta.url)
    }));
}
