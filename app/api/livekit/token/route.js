import { NextResponse } from 'next/server';
import { AccessToken } from 'livekit-server-sdk';

/**
 * Generate Livekit access token for client connections
 * POST /api/livekit/token
 */
export async function POST(request) {
    try {
        const { roomName, participantName, participantMetadata } = await request.json();

        // Validate required parameters
        if (!roomName || !participantName) {
            return NextResponse.json(
                { error: 'roomName and participantName are required' },
                { status: 400 }
            );
        }

        // Get Livekit credentials from environment
        const apiKey = process.env.LIVEKIT_API_KEY;
        const apiSecret = process.env.LIVEKIT_API_SECRET;
        const wsUrl = process.env.LIVEKIT_URL;

        if (!apiKey || !apiSecret || !wsUrl) {
            console.error('Missing Livekit environment variables');
            return NextResponse.json(
                { error: 'Livekit configuration not found' },
                { status: 500 }
            );
        }

        // Create access token
        const token = new AccessToken(apiK<PERSON>, apiSecret, {
            identity: participantName,
            name: participantN<PERSON>,
            metadata: participantMetadata || JSON.stringify({ role: 'user' }),
        });

        // Grant permissions
        token.addGrant({
            room: roomName,
            roomJoin: true,
            canPublish: true,
            canSubscribe: true,
            canPublishData: true,
            // Audio permissions for voice input
            canPublishSources: ['microphone'],
        });

        // Generate JWT token
        const jwt = await token.toJwt();

        return NextResponse.json({
            token: jwt,
            wsUrl: wsUrl,
            roomName: roomName,
            participantName: participantName,
        });

    } catch (error) {
        console.error('Error generating Livekit token:', error);
        return NextResponse.json(
            { error: 'Failed to generate token' },
            { status: 500 }
        );
    }
}

/**
 * Health check endpoint
 * GET /api/livekit/token
 */
export async function GET() {
    return NextResponse.json({
        status: 'ok',
        service: 'livekit-token-generator',
        timestamp: new Date().toISOString(),
    });
}
