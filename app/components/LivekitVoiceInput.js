'use client'

import { useState, useEffect, useRef, useCallback } from 'react';
import { Room, RoomEvent, ConnectionState, DataPacket_Kind } from 'livekit-client';

/**
 * Livekit Voice Input Component
 * Replaces the Web Speech API with Livekit Agent-based transcription
 */
const LivekitVoiceInput = ({ 
    lng, 
    onTranscription, 
    onStatusChange,
    enabled = true 
}) => {
    const [room, setRoom] = useState(null);
    const [isConnected, setIsConnected] = useState(false);
    const [isConnecting, setIsConnecting] = useState(false);
    const [isListening, setIsListening] = useState(false);
    const [error, setError] = useState(null);
    const [interimText, setInterimText] = useState('');
    
    const roomRef = useRef(null);
    const audioTrackRef = useRef(null);

    // Generate unique room and participant names
    const generateRoomName = useCallback(() => {
        return `voice-transcription-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }, []);

    const generateParticipantName = useCallback(() => {
        return `user-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
    }, []);

    // Handle messages from the Livekit agent
    const handleAgentMessage = useCallback((message) => {
        console.log('Agent message:', message);

        switch (message.type) {
            case 'transcription_interim':
                setInterimText(message.text || '');
                break;

            case 'transcription_final':
                const finalText = message.text || '';
                if (finalText.trim()) {
                    onTranscription?.(finalText);
                    setInterimText('');
                }
                break;

            case 'transcription_error':
                console.error('Transcription error:', message.error);
                setError(message.error);
                onStatusChange?.('error');
                break;

            case 'transcription_status':
                if (message.status === 'listening') {
                    setIsListening(true);
                } else if (message.status === 'stopped') {
                    setIsListening(false);
                    setInterimText('');
                }
                break;

            case 'agent_status':
                console.log('Agent status:', message.message);
                break;

            default:
                console.log('Unknown agent message type:', message.type);
        }
    }, [onTranscription, onStatusChange]);

    // Connect to Livekit room
    const connect = useCallback(async () => {
        if (isConnecting || isConnected) return;

        try {
            setIsConnecting(true);
            setError(null);
            onStatusChange?.('connecting');

            // Generate room and participant names
            const roomName = generateRoomName();
            const participantName = generateParticipantName();

            // Get access token from API
            const tokenResponse = await fetch('/api/livekit/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    roomName,
                    participantName,
                    participantMetadata: JSON.stringify({
                        role: 'user',
                        language: lng,
                        timestamp: Date.now(),
                    }),
                }),
            });

            if (!tokenResponse.ok) {
                throw new Error('Failed to get access token');
            }

            const { token, wsUrl } = await tokenResponse.json();

            // Create and connect to room
            const newRoom = new Room();
            roomRef.current = newRoom;
            setRoom(newRoom);

            // Set up event listeners
            newRoom.on(RoomEvent.Connected, () => {
                console.log('Connected to Livekit room');
                setIsConnected(true);
                setIsConnecting(false);
                onStatusChange?.('connected');
            });

            newRoom.on(RoomEvent.Disconnected, () => {
                console.log('Disconnected from Livekit room');
                setIsConnected(false);
                setIsListening(false);
                setInterimText('');
                onStatusChange?.('disconnected');
            });

            newRoom.on(RoomEvent.ConnectionStateChanged, (state) => {
                console.log('Connection state changed:', state);
                if (state === ConnectionState.Failed) {
                    setError('Connection failed');
                    onStatusChange?.('error');
                }
            });

            // Listen for data messages from agent
            newRoom.on(RoomEvent.DataReceived, (payload, participant) => {
                try {
                    const message = JSON.parse(new TextDecoder().decode(payload));
                    handleAgentMessage(message);
                } catch (error) {
                    console.error('Error parsing agent message:', error);
                }
            });

            // Connect to room
            await newRoom.connect(wsUrl, token);

        } catch (error) {
            console.error('Error connecting to Livekit:', error);
            setError(error.message);
            setIsConnecting(false);
            onStatusChange?.('error');
        }
    }, [isConnecting, isConnected, lng, onStatusChange, generateRoomName, generateParticipantName, handleAgentMessage]);

    // Disconnect from Livekit room
    const disconnect = useCallback(async () => {
        if (!room) return;

        try {
            setIsListening(false);
            setInterimText('');
            
            // Stop audio track if active
            if (audioTrackRef.current) {
                audioTrackRef.current.stop();
                audioTrackRef.current = null;
            }

            // Disconnect from room
            await room.disconnect();
            setRoom(null);
            roomRef.current = null;
            setIsConnected(false);
            onStatusChange?.('disconnected');

        } catch (error) {
            console.error('Error disconnecting from Livekit:', error);
            setError(error.message);
        }
    }, [room, onStatusChange]);

    // Start listening (enable microphone)
    const startListening = useCallback(async () => {
        if (!room || !isConnected || isListening) return;

        try {
            setError(null);
            onStatusChange?.('listening');

            // Enable microphone and publish audio track
            await room.localParticipant.enableCameraAndMicrophone(false, true);
            
            // Get the audio track
            const audioTrack = room.localParticipant.audioTrackPublications.values().next().value?.track;
            if (audioTrack) {
                audioTrackRef.current = audioTrack;
                setIsListening(true);
            } else {
                throw new Error('Failed to get audio track');
            }

        } catch (error) {
            console.error('Error starting listening:', error);
            setError(error.message);
            onStatusChange?.('error');
        }
    }, [room, isConnected, isListening, onStatusChange]);

    // Stop listening (disable microphone)
    const stopListening = useCallback(async () => {
        if (!room || !isListening) return;

        try {
            // Disable microphone
            await room.localParticipant.setMicrophoneEnabled(false);
            
            if (audioTrackRef.current) {
                audioTrackRef.current.stop();
                audioTrackRef.current = null;
            }

            setIsListening(false);
            setInterimText('');
            onStatusChange?.('connected');

        } catch (error) {
            console.error('Error stopping listening:', error);
            setError(error.message);
        }
    }, [room, isListening, onStatusChange]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (roomRef.current) {
                roomRef.current.disconnect();
            }
        };
    }, []);

    // Toggle connection
    const toggleConnection = useCallback(async () => {
        if (isConnected) {
            await disconnect();
        } else {
            await connect();
        }
    }, [isConnected, connect, disconnect]);

    // Toggle listening
    const toggleListening = useCallback(async () => {
        if (isListening) {
            await stopListening();
        } else {
            await startListening();
        }
    }, [isListening, startListening, stopListening]);

    return {
        // State
        isConnected,
        isConnecting,
        isListening,
        error,
        interimText,
        enabled: enabled && typeof window !== 'undefined',
        
        // Actions
        connect,
        disconnect,
        startListening,
        stopListening,
        toggleConnection,
        toggleListening,
    };
};

export default LivekitVoiceInput;
