"use client";

import {
    useState,
    useEffect,
    useRef,
} from 'react';
import AnonymousHeader from '../components/AnonymousHeader';
import UserHeader from '../components/UserHeader';
import MessagesList from './MessagesList';
import ChatForm from '../components/ChatForm';
import Footer from '../components/Footer';
import { useChat } from '@ai-sdk/react';
import {
    envBoolean,
    getMediaUrl,
    deleteConversationId,
    getConversationId,
    getDeviceId,
    getDbTranslation,
    getSupportedLanguages,
    shouldUseRealTimeTranslation,
    debug,
} from '../lib/helpers';
import { useTranslation } from '../i18n/client';
import EmptyState from './EmptyState';
import { languages } from '../i18n/settings';
import { trackShare } from '../lib/tracking';
import { DefaultChatTransport } from 'ai';

const stream = envBoolean(process.env.NEXT_PUBLIC_STREAM);

export default function Body({
    lng,
    agent,
    queryParams,
    theme,
    user,
    userId,
    sharedMessages,
    agentQuestions,
}) {

    const agentId = parseInt(agent.id);
    const model = queryParams.model;

    // Operating modes & logging
    const hideHeader = agent.hide_header;
    const disableAutoScroll = (queryParams.autoscroll && (queryParams.autoscroll === 'false'));
    const anonymous = queryParams.anonymous;
    const realTimeTranslation = shouldUseRealTimeTranslation(
        lng,
        process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
        agent.auto_translate,
        agent.auto_translate_languages
    );
    const parentUrl = queryParams.parent_url;
    const parentHost = queryParams.parent_host;

    // Languages and translations
    const translation = queryParams.translation || agent.default_translation || process.env.NEXT_PUBLIC_DEFAULT_TRANSLATION;
    const supportedLanguages = queryParams.languages ? queryParams.languages.split(',').map(language => language.trim()) : getSupportedLanguages(agent, languages);
    const possibleTranslations = JSON.parse(process.env.NEXT_PUBLIC_TRANSLATIONS);
    const passedTranslations = queryParams.translations ? queryParams.translations.split(',').map(translation => translation.trim()) : [];
    const supportedTranslations = queryParams.translations ?
        Object.fromEntries(Object.entries(possibleTranslations).filter(([k]) => passedTranslations.includes(k))) :
        (agent.supported_translations?.length > 0) ?
            Object.fromEntries(Object.entries(possibleTranslations).filter(([k]) => agent.supported_translations.includes(k))) :
            possibleTranslations
    ;

    // Creator info
    const agentCreatorName = getDbTranslation(agent.creator_name, lng);
    const agentImage = agent.image_path ? getMediaUrl(agent.image_path) : null;
    const agentDescription = getDbTranslation(agent.creator_description, lng);
    const agentQuestionsTitle = getDbTranslation(agent.questions_title, lng);
    const agentCreatorUrl = getDbTranslation(agent.creator_url, lng);
    const isOpenSource = agent.is_open_source;
    const licenseUrl = agent.license_url;

    // Intro messaging
    const introPreamble = getMessaging(queryParams.preamble, agent.intro_preamble, lng);
    const introHeadline = getMessaging(queryParams.headline, agent.intro_headline, lng);
    const introDescription = getMessaging(queryParams.description, agent.intro_description, lng);

    // Footer customizations
    const footerText = getDbTranslation(agent.footer_text, lng);
    const hideFooterCta = agent.hide_footer_cta;
    const footerCtaLabel = getDbTranslation(agent.footer_cta_label, lng);
    const footerCtaUrl = getDbTranslation(agent.footer_cta_url, lng);
    const showMedia = (agent.show_media && agent.has_semantic_search && envBoolean(process.env.NEXT_PUBLIC_MEDIA)); // Agent must opt-in and global flag must be set
    const mediaCollectionIds = agent.media_collections;

    // Response footer messaging
    const responseFooterContent = getDbTranslation(agent.response_footer_content, lng);
    const responseFooterThreshold = agent.response_footer_threshold;

    function getMessaging(queryParam, agentFallback, lng) {
        if (queryParam) {
            if (queryParam.substring(0, 1) === '{') {
                return getDbTranslation(JSON.parse(queryParam), lng);
            } else {
                return queryParam;
            }
        } else {
            return getDbTranslation(agentFallback, lng);
        }
    }

    const [isEmbedded, setIsEmbedded] = useState(false);
    const [responding, setResponding] = useState(false);
    const [streaming, setStreaming] = useState(false);
    const [error, setError] = useState(null);
    const [currentPromptId, setCurrentPromptId] = useState(null);
    const { t } = useTranslation(lng);

    useEffect(() => {
        setIsEmbedded((typeof window !== 'undefined') && (window.self !== window.top));
    }, []);

    let conversationDelete = useRef(false);
    useEffect(() => {
        if (!conversationDelete.hasRun) {
            deleteConversationId();
            conversationDelete.hasRun = true;
        }
    }, []);

    const { messages, sendMessage, status } = useChat({

        messages: sharedMessages,

        transport: new DefaultChatTransport({

            api: process.env.NEXT_PUBLIC_COMPLETION_INTERNAL_ENDPOINT,

            prepareSendMessagesRequest: ({ id, messages }) => {

                return {
                    body: {
                        id,
                        messages,
                        model,
                        user: userId,
                        metadata: {
                            language: lng ?? process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
                            translation: translation ?? null,
                            anonymous: anonymous ?? false,
                            parent_url: parentUrl ?? null,
                            parent_host: parentHost ?? null,
                            shared_prompt: sharedPrompt ? parseInt(sharedPrompt.metadata.id) : null,
                            device: getDeviceId(),
                            conversation: getConversationId(),
                        },
                    }
                };
            },

        }),

        onFinish: async ({ message }) => {
            setTimeout(() => document.getElementById('apg-prompt-input').focus(), 0);
        },

    });

    useEffect(() => {
        setResponding(['submitted', 'streaming'].includes(status));
        setStreaming(messages[messages.length-1]?.metadata?.streaming);
        setError(['error'].includes(status));
    }, [status, messages]);

    const sharedPrompt = (sharedMessages.length > 0) ? sharedMessages[sharedMessages.length-1] : null;
    let shareStatus = useRef(false);
    useEffect(() => {
        if (sharedPrompt && sharedPrompt.metadata.id && !shareStatus.hasRun) {
            trackShare(
                sharedPrompt.metadata.id,
                user ? user.id : null,
                agentId
            );
            shareStatus.hasRun = true;
        }
    }, [user, agentId]);

    return (

        <div id="apg-container" className="h-full relative flex flex-col">

            {(!hideHeader || !isEmbedded) && user && (
                <UserHeader
                    t={t}
                    lng={lng}
                    translation={translation}
                    user={user}
                    supportedLanguages={supportedLanguages}
                    supportedTranslations={supportedTranslations}
                />
            )}

            <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-full flex flex-col">

                {(!hideHeader || !isEmbedded) && !user && (
                    <AnonymousHeader
                        t={t}
                        lng={lng}
                        translation={translation}
                        isEmbedded={isEmbedded}
                        theme={theme}
                        supportedLanguages={supportedLanguages}
                        supportedTranslations={supportedTranslations}
                        agentCreatorName={agentCreatorName}
                        agentImage={agentImage}
                        agentDescription={agentDescription}
                        agentCreatorUrl={agentCreatorUrl}
                        isOpenSource={isOpenSource}
                        licenseUrl={licenseUrl}
                    />
                )}

                <main
                    id="apg-body"
                    className={`
                        flex-auto 
                        grow 
                        w-full 
                        ${hideHeader ? 'mt-4' : ''}
                    `}
                >
                    <div className="h-full flex flex-col relative">

                        {((messages.length === 0) && !sharedPrompt) ? (
                            <EmptyState
                                t={t}
                                sendMessage={sendMessage}
                                introPreamble={introPreamble}
                                introHeadline={introHeadline}
                                introDescription={introDescription}
                                agentQuestionsTitle={agentQuestionsTitle}
                                agentQuestions={agentQuestions}
                            />
                        ) : (
                            <MessagesList
                                t={t}
                                messages={messages}
                                responding={responding}
                                streaming={streaming}
                                error={error}
                                agentId={agentId}
                                lng={lng}
                                userId={userId}
                                realTimeTranslation={realTimeTranslation}
                                responseFooterContent={responseFooterContent}
                                responseFooterThreshold={responseFooterThreshold}
                                disableAutoScroll={disableAutoScroll}
                                isEmbedded={isEmbedded}
                                showMedia={showMedia}
                                mediaCollectionIds={mediaCollectionIds}
                                currentPromptId={currentPromptId}
                            />
                        )}

                        <ChatForm
                            sendMessage={sendMessage}
                            lng={lng}
                            t={t}
                            responding={responding}
                            setResponding={setResponding}
                            setStreaming={setStreaming}
                        />

                    </div>
                </main>

                <Footer
                    t={t}
                    isEmbedded={isEmbedded}
                    footerText={footerText}
                    hideFooterCta={hideFooterCta}
                    footerCtaLabel={footerCtaLabel}
                    footerCtaUrl={footerCtaUrl}
                />

            </div>

        </div>

    );

}
